import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert, TextStyle } from 'react-native';
import FilledButton from '~/components/buttons/FilledButton';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import { primarySolidButton } from '~/styles/buttons';
import colors from '~/styles/colors';
import { calculateLaplacianVariance, BlurDetectionResult } from '~/utils/blurDetection';

interface BlurDetectionTesterProps {
  testID?: string;
}

const BlurDetectionTester: React.FC<BlurDetectionTesterProps> = ({ testID }) => {
  const [imageData, setImageData] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<BlurDetectionResult | null>(null);

  const handleImageCapture = (base64Image: string) => {
    setImageData(base64Image);
    setResult(null); // Clear previous results
  };

  const handleAnalyzeBlur = async () => {
    if (!imageData) {
      Alert.alert('No Image', 'Please capture an image first');
      return;
    }

    setIsProcessing(true);
    try {
      const blurResult = await calculateLaplacianVariance(imageData);
      setResult(blurResult);
    } catch (error) {
      console.error('Blur analysis error:', error);
      Alert.alert('Error', 'Failed to analyze image for blur');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClearTest = () => {
    setImageData('');
    setResult(null);
  };

  return (
    <View style={styles.container} testID={testID}>
      <Text style={styles.title}>Blur Detection Tester</Text>
      <Text style={styles.subtitle}>
        Capture an image and test Laplacian variance calculation
      </Text>

      <View style={styles.section}>
        <PhotoCaptureWrapper
          imageData={imageData}
          imageTitle="Test Image"
          cameraTitle="Capture Test Image"
          onCapture={handleImageCapture}
          multiple={false}
        />
      </View>

      {imageData && (
        <View style={styles.section}>
          <FilledButton
            id="BlurTester.AnalyzeButton"
            title={isProcessing ? "Analyzing..." : "Analyze Blur"}
            onClick={handleAnalyzeBlur}
            isDisabled={isProcessing}
            style={primarySolidButton}
            color="primary"
            testID="BlurTester.AnalyzeButton"
          />
        </View>
      )}

      {result && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Analysis Results</Text>
          
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Laplacian Variance:</Text>
            <Text style={styles.resultValue}>{result.variance}</Text>
          </View>
          
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Sharpness Level:</Text>
            <Text style={[styles.resultValue, { color: result.isBlurry ? colors.red600 : colors.greenDark }]}>
              {result.sharpnessLevel}
            </Text>
          </View>

          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Is Blurry:</Text>
            <Text style={[styles.resultValue, { color: result.isBlurry ? colors.red600 : colors.greenDark }]}>
              {result.isBlurry ? 'Yes' : 'No'}
            </Text>
          </View>

          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Processing Time:</Text>
            <Text style={styles.resultValue}>{result.processingTime}ms</Text>
          </View>
        </View>
      )}

      {(imageData || result) && (
        <View style={styles.section}>
          <FilledButton
            id="BlurTester.ClearButton"
            title="Clear Test"
            onClick={handleClearTest}
            style={[primarySolidButton, { backgroundColor: colors.buttonGray }]}
            color="secondary"
            testID="BlurTester.ClearButton"
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    marginVertical: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '500',
    color: colors.darkBlue600,
    marginBottom: 4,
  } as TextStyle,
  subtitle: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.darkGray,
    marginBottom: 16,
  } as TextStyle,
  section: {
    marginVertical: 8,
  },
  resultsContainer: {
    backgroundColor: colors.lightGray,
    padding: 12,
    borderRadius: 6,
    marginVertical: 8,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: colors.darkBlue600,
    marginBottom: 8,
  } as TextStyle,
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  resultLabel: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.darkGray,
    flex: 1,
  } as TextStyle,
  resultValue: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.darkBlue600,
    flex: 1,
    textAlign: 'right',
  } as TextStyle,
});

export default BlurDetectionTester;
