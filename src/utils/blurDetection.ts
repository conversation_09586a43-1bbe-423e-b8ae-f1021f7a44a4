/**
 * Blur Detection Utility using Laplacian Variance
 *
 * This module provides functionality to detect blur in images by calculating
 * the Laplacian variance. The Laplacian operator is used to detect edges,
 * and the variance of the result indicates the sharpness of the image.
 *
 * Lower variance values indicate more blur, higher values indicate sharper images.
 *
 * NOTE: This is a simplified implementation for testing purposes.
 * For production use, consider using native image processing libraries.
 */

export interface BlurDetectionResult {
  variance: number;
  isBlurry: boolean;
  sharpnessLevel: string;
  processingTime: number;
}

/**
 * Decode base64 image data to get pixel information
 * This is a simplified approach for testing - in production you'd want to use
 * native image processing libraries for better performance
 */
const decodeBase64Image = (base64Data: string): Promise<{
  width: number;
  height: number;
  pixels: number[];
}> => {
  return new Promise((resolve, reject) => {
    // For now, we'll simulate image processing with mock data
    // In a real implementation, you'd decode the base64 and extract pixel data

    // Mock implementation - generates sample data for testing
    const mockWidth = 100;
    const mockHeight = 100;
    const mockPixels: number[] = [];

    // Generate mock grayscale pixel data
    for (let i = 0; i < mockWidth * mockHeight; i++) {
      // Create some variation to simulate real image data
      const baseValue = 128 + Math.sin(i / 100) * 50;
      const noise = (Math.random() - 0.5) * 20;
      mockPixels.push(Math.max(0, Math.min(255, baseValue + noise)));
    }

    resolve({
      width: mockWidth,
      height: mockHeight,
      pixels: mockPixels
    });
  });
};

/**
 * Apply Laplacian kernel to detect edges
 * @param pixels - Grayscale pixel data
 * @param width - Image width
 * @param height - Image height
 * @returns Laplacian filtered image data
 */
const applyLaplacianKernel = (pixels: number[], width: number, height: number): number[] => {
  const laplacian: number[] = [];

  // Laplacian kernel (4-connected for simplicity)
  const kernel = [
    [0, -1, 0],
    [-1, 4, -1],
    [0, -1, 0]
  ];

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      let sum = 0;

      // Apply kernel
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const pixelY = y + ky;
          const pixelX = x + kx;

          // Handle boundary conditions by using nearest pixel
          const boundedY = Math.max(0, Math.min(height - 1, pixelY));
          const boundedX = Math.max(0, Math.min(width - 1, pixelX));

          const pixelIndex = boundedY * width + boundedX;
          const kernelValue = kernel[ky + 1][kx + 1];

          sum += pixels[pixelIndex] * kernelValue;
        }
      }

      laplacian.push(sum);
    }
  }

  return laplacian;
};

/**
 * Calculate variance of an array
 * @param data - Array of numbers
 * @returns Variance value
 */
const calculateVariance = (data: number[]): number => {
  if (data.length === 0) return 0;

  const mean = data.reduce((sum, value) => sum + value, 0) / data.length;
  const squaredDifferences = data.map(value => Math.pow(value - mean, 2));
  const variance = squaredDifferences.reduce((sum, value) => sum + value, 0) / data.length;
  return variance;
};

/**
 * Determine sharpness level based on variance value
 * @param variance - Calculated variance
 * @returns Object with blur assessment
 */
const assessSharpness = (variance: number): { isBlurry: boolean; sharpnessLevel: string } => {
  // These thresholds are estimates and may need adjustment based on testing
  if (variance < 100) {
    return { isBlurry: true, sharpnessLevel: 'Very Blurry' };
  } else if (variance < 300) {
    return { isBlurry: true, sharpnessLevel: 'Blurry' };
  } else if (variance < 600) {
    return { isBlurry: false, sharpnessLevel: 'Slightly Sharp' };
  } else if (variance < 1000) {
    return { isBlurry: false, sharpnessLevel: 'Sharp' };
  } else {
    return { isBlurry: false, sharpnessLevel: 'Very Sharp' };
  }
};

/**
 * Calculate Laplacian variance for blur detection
 * @param base64Image - Base64 encoded image data
 * @returns Promise resolving to blur detection result
 */
export const calculateLaplacianVariance = async (base64Image: string): Promise<BlurDetectionResult> => {
  const startTime = Date.now();

  try {
    // Decode image data
    const { width, height, pixels } = await decodeBase64Image(base64Image);

    // Apply Laplacian kernel
    const laplacianData = applyLaplacianKernel(pixels, width, height);

    // Calculate variance
    const variance = calculateVariance(laplacianData);

    // Assess sharpness
    const { isBlurry, sharpnessLevel } = assessSharpness(variance);

    const processingTime = Date.now() - startTime;

    return {
      variance: Math.round(variance * 100) / 100, // Round to 2 decimal places
      isBlurry,
      sharpnessLevel,
      processingTime
    };
  } catch (error) {
    console.error('Error calculating Laplacian variance:', error);
    throw new Error('Failed to process image for blur detection');
  }
};
